#!/usr/bin/env python3
"""
中文输入法 (Fcitx5 + Rime)
支持平台: Ubuntu/Debian
架构: x86_64
"""

import os
import sys
import time

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info, run_command_sync

def get_latest_version():
    return "5.0.14"

def download(temp_dir, download_now=True):
    version = get_latest_version()
    if not download_now:
        return version, None
    return version, None

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    run_command_sync("sudo apt-get update", capture_output=True)

    packages = [
        "fcitx5", "fcitx5-rime", "fcitx5-chinese-addons",
        "fcitx5-frontend-gtk3", "fcitx5-frontend-gtk2",
        "fcitx5-frontend-qt5", "fcitx5-config-qt",
        "qtwayland5", "ibus-rime"
    ]

    result = run_command_sync(f"sudo apt-get install -y {' '.join(packages)}", capture_output=True)
    if result.returncode != 0:
        print("❌ 安装失败")
        return False

    _, user_home, uid, gid = get_user_info()

    xinputrc_path = os.path.join(user_home, '.xinputrc')
    if os.path.exists(xinputrc_path):
        run_command_sync(f"rm -f '{xinputrc_path}'", capture_output=True)

    run_command_sync("im-config -n fcitx5", capture_output=True)

    profile_path = os.path.join(user_home, '.profile')
    env_vars = [
        "export GTK_IM_MODULE=fcitx",
        "export QT_IM_MODULE=fcitx",
        "export XMODIFIERS=@im=fcitx",
        "export INPUT_METHOD=fcitx",
        "export SDL_IM_MODULE=fcitx",
        "export GLFW_IM_MODULE=ibus"
    ]

    for var in ["GTK_IM_MODULE=fcitx", "QT_IM_MODULE=fcitx", "XMODIFIERS=@im=fcitx", "INPUT_METHOD=fcitx", "SDL_IM_MODULE=fcitx", "GLFW_IM_MODULE=ibus"]:
        run_command_sync(f"sed -i '/{var}/d' '{profile_path}'", capture_output=True)

    for env_var in env_vars:
        run_command_sync(f"echo '{env_var}' >> '{profile_path}'", capture_output=True)

    run_command_sync(f"chown {uid}:{gid} '{profile_path}'", capture_output=True)

    fcitx5_config_dir = os.path.join(user_home, '.config/fcitx5')
    run_command_sync(f"mkdir -p '{fcitx5_config_dir}'", capture_output=True)

    profile_content = """[Groups/0]
Name=默认
Default Layout=us
DefaultIM=rime

[Groups/0/Items/0]
Name=keyboard-us
Layout=

[Groups/0/Items/1]
Name=rime
Layout=

[GroupOrder]
0=默认"""

    profile_config_path = os.path.join(fcitx5_config_dir, 'profile')
    try:
        with open(profile_config_path, 'w', encoding='utf-8') as f:
            f.write(profile_content)
        run_command_sync(f"chown {uid}:{gid} '{profile_config_path}'", capture_output=True)
    except Exception:
        pass

    script_content = """#!/bin/bash
export QT_QPA_PLATFORM=wayland
export PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
unset LD_LIBRARY_PATH
unset LD_PRELOAD
fcitx5-configtool
"""

    script_path = os.path.join(user_home, 'fcitx5-config')
    try:
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        run_command_sync(f"chmod +x '{script_path}'", capture_output=True)
        run_command_sync(f"chown {uid}:{gid} '{script_path}'", capture_output=True)
    except Exception:
        pass

    run_command_sync("pkill -f fcitx5", capture_output=True, silent=True)
    run_command_sync("nohup fcitx5 > /dev/null 2>&1 &", capture_output=True, silent=True)

    time.sleep(2)

    run_command_sync("fcitx5-remote -r", capture_output=True, silent=True)

    print("✅ 中文输入法安装完成")
    return True

def uninstall():
    run_command_sync("pkill -f fcitx5", capture_output=True, silent=True)

    packages = [
        "fcitx5", "fcitx5-rime", "fcitx5-chinese-addons",
        "fcitx5-frontend-gtk3", "fcitx5-frontend-gtk2",
        "fcitx5-frontend-qt5", "fcitx5-config-qt",
        "qtwayland5", "ibus-rime"
    ]

    for package in packages:
        run_command_sync(f"sudo apt-get remove --purge -y {package}", capture_output=True, silent=True)

    run_command_sync("sudo snap remove fcitx5", capture_output=True, silent=True)

    _, user_home, _, _ = get_user_info()
    config_files = [
        os.path.join(user_home, '.xinputrc'),
        os.path.join(user_home, '.config/fcitx5'),
        os.path.join(user_home, '.local/share/fcitx5'),
        os.path.join(user_home, 'fcitx5-config')
    ]

    for config_file in config_files:
        if os.path.exists(config_file):
            run_command_sync(f"rm -rf '{config_file}'", capture_output=True)

    profile_path = os.path.join(user_home, '.profile')
    env_vars_to_remove = [
        "GTK_IM_MODULE=fcitx",
        "QT_IM_MODULE=fcitx",
        "XMODIFIERS=@im=fcitx",
        "INPUT_METHOD=fcitx",
        "SDL_IM_MODULE=fcitx",
        "GLFW_IM_MODULE=ibus"
    ]

    for var in env_vars_to_remove:
        run_command_sync(f"sed -i '/{var}/d' '{profile_path}'", capture_output=True, silent=True)

    print("✅ 卸载完成")
    return True

def check_status():
    result = run_command_sync("dpkg -l | grep -E '^ii.*fcitx5[^-]'", capture_output=True, silent=True)
    fcitx5_installed = result.returncode == 0 and result.stdout.strip()

    result = run_command_sync("dpkg -l | grep -E '^ii.*fcitx5-rime'", capture_output=True, silent=True)
    rime_installed = result.returncode == 0 and result.stdout.strip()

    result = run_command_sync("dpkg -l | grep -E '^ii.*qtwayland5'", capture_output=True, silent=True)
    qtwayland_installed = result.returncode == 0 and result.stdout.strip()

    result = run_command_sync("dpkg -l | grep -E '^ii.*ibus-rime'", capture_output=True, silent=True)
    ibus_rime_installed = result.returncode == 0 and result.stdout.strip()

    if fcitx5_installed and rime_installed and qtwayland_installed:
        print('🟢 状态: 已安装')
        return 0
    elif fcitx5_installed or ibus_rime_installed:
        print('🟡 状态: 部分安装')
        return 1
    else:
        print('🔴 状态: 未安装')
        return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
